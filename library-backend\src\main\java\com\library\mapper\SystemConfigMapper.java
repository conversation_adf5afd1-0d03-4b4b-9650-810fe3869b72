package com.library.mapper;

import com.library.entity.SystemConfig;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface SystemConfigMapper {
    
    @Select("SELECT * FROM system_config WHERE config_key = #{key}")
    SystemConfig findByKey(String key);
    
    @Select("SELECT * FROM system_config")
    List<SystemConfig> findAll();
    
    @Insert("INSERT INTO system_config(config_key, config_value, description) " +
            "VALUES(#{configKey}, #{configValue}, #{description})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SystemConfig config);
    
    @Update("UPDATE system_config SET config_value = #{configValue}, " +
            "description = #{description} WHERE id = #{id}")
    int update(SystemConfig config);
    
    @Delete("DELETE FROM system_config WHERE id = #{id}")
    int deleteById(Long id);
}