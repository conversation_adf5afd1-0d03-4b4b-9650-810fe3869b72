package com.library.service;

import com.library.entity.Notification;
import com.library.entity.Reservation;
import com.library.mapper.BookMapper;
import com.library.mapper.NotificationMapper;
import com.library.mapper.ReservationMapper;
import com.library.mapper.SystemConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ReservationService {
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    @Autowired
    private BookMapper bookMapper;
    
    @Autowired
    private SystemConfigMapper systemConfigMapper;
    
    @Autowired
    private NotificationMapper notificationMapper;
    
    public List<Reservation> findByUserId(Long userId) {
        return reservationMapper.findByUserId(userId);
    }
    
    public Reservation findById(Long id) {
        return reservationMapper.findById(id);
    }
    
    @Transactional
    public String createReservation(Long userId, Long bookId) {
        // 检查是否允许预约
        var allowReservationConfig = systemConfigMapper.findByKey("allow_reservation");
        if (!"true".equals(allowReservationConfig.getConfigValue())) {
            return "系统暂不支持预约功能";
        }
        
        // 检查图书是否存在
        var book = bookMapper.findById(bookId);
        if (book == null) {
            return "图书不存在";
        }
        
        // 检查图书是否有库存（有库存不需要预约）
        if (book.getAvailableStock() > 0) {
            return "该图书有库存，可直接借阅";
        }
        
        // 检查是否已经预约
        var existingReservation = reservationMapper.findActiveReservation(userId, bookId);
        if (existingReservation != null) {
            return "您已预约该图书";
        }
        
        // 创建预约
        Reservation reservation = new Reservation();
        reservation.setUserId(userId);
        reservation.setBookId(bookId);
        reservation.setStatus("WAITING");
        
        if (reservationMapper.insert(reservation) > 0) {
            // 计算排队位置
            var waitingList = reservationMapper.findWaitingByBookId(bookId);
            int position = waitingList.size();
            
            // 发送预约成功通知
            Notification notification = new Notification();
            notification.setUserId(userId);
            notification.setTitle("预约成功");
            notification.setContent(String.format("您已成功预约《%s》，当前排队位置：%d", 
                    book.getTitle(), position));
            notification.setType("RESERVATION");
            notificationMapper.insert(notification);
            
            return null; // 成功
        }
        
        return "预约失败";
    }
    
    @Transactional
    public String cancelReservation(Long reservationId) {
        var reservation = reservationMapper.findById(reservationId);
        if (reservation == null || !"WAITING".equals(reservation.getStatus())) {
            return "预约不存在或已处理";
        }
        
        reservation.setStatus("CANCELLED");
        if (reservationMapper.update(reservation) > 0) {
            // 发送取消通知
            Notification notification = new Notification();
            notification.setUserId(reservation.getUserId());
            notification.setTitle("预约已取消");
            notification.setContent(String.format("您对《%s》的预约已取消", 
                    reservation.getBook().getTitle()));
            notification.setType("RESERVATION");
            notificationMapper.insert(notification);
            
            return null; // 成功
        }
        
        return "取消预约失败";
    }
    
    @Transactional
    public void checkExpiredReservations() {
        var now = LocalDateTime.now();
        // 这里需要额外的查询来找出过期的预约
        // 实际实现时可以添加一个专门的查询方法
    }
}