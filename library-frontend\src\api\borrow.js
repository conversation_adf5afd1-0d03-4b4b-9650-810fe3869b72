import api from './index'

export const borrowApi = {
  // 获取所有借阅记录（管理员）
  getAll() {
    return api.get('/borrow')
  },
  
  // 获取我的借阅记录
  getMyRecords() {
    return api.get('/borrow/my')
  },
  
  // 获取指定用户的借阅记录（管理员）
  getUserRecords(userId) {
    return api.get(`/borrow/user/${userId}`)
  },
  
  // 借阅图书
  borrowBook(bookId) {
    return api.post('/borrow/borrow', { bookId })
  },
  
  // 归还图书
  returnBook(recordId) {
    return api.post(`/borrow/return/${recordId}`)
  },
  
  // 续借图书
  renewBook(recordId) {
    return api.post(`/borrow/renew/${recordId}`)
  },
  
  // 获取逾期记录（管理员）
  getOverdueRecords() {
    return api.get('/borrow/overdue')
  }
}