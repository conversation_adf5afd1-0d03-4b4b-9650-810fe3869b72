package com.library.mapper;

import com.library.entity.Reservation;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface ReservationMapper {
    
    @Select("SELECT * FROM reservation WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "bookId", column = "book_id"),
        @Result(property = "user", column = "user_id",
                one = @One(select = "com.library.mapper.UserMapper.findById")),
        @Result(property = "book", column = "book_id",
                one = @One(select = "com.library.mapper.BookMapper.findById"))
    })
    Reservation findById(Long id);
    
    @Select("SELECT * FROM reservation WHERE user_id = #{userId} ORDER BY reservation_date DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "bookId", column = "book_id"),
        @Result(property = "user", column = "user_id",
                one = @One(select = "com.library.mapper.UserMapper.findById")),
        @Result(property = "book", column = "book_id",
                one = @One(select = "com.library.mapper.BookMapper.findById"))
    })
    List<Reservation> findByUserId(Long userId);
    
    @Select("SELECT * FROM reservation WHERE book_id = #{bookId} AND status = 'WAITING' ORDER BY reservation_date")
    List<Reservation> findWaitingByBookId(Long bookId);
    
    @Select("SELECT * FROM reservation WHERE user_id = #{userId} AND book_id = #{bookId} AND status = 'WAITING'")
    Reservation findActiveReservation(@Param("userId") Long userId, @Param("bookId") Long bookId);
    
    @Insert("INSERT INTO reservation(user_id, book_id, status) VALUES(#{userId}, #{bookId}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Reservation reservation);
    
    @Update("UPDATE reservation SET status = #{status}, notify_date = #{notifyDate}, expire_date = #{expireDate} WHERE id = #{id}")
    int update(Reservation reservation);
    
    @Delete("DELETE FROM reservation WHERE id = #{id}")
    int deleteById(Long id);
}