package com.library.utils;

import com.library.entity.Book;
import com.library.entity.BorrowRecord;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Component
public class ExcelUtils {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public byte[] exportBooks(List<Book> books) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("图书列表");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ISBN", "书名", "作者", "出版社", "出版日期", "库存", "可借数量", "借出数量", "总借阅次数"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                CellStyle style = workbook.createCellStyle();
                Font font = workbook.createFont();
                font.setBold(true);
                style.setFont(font);
                cell.setCellStyle(style);
            }
            
            // 填充数据
            int rowNum = 1;
            for (Book book : books) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(book.getIsbn());
                row.createCell(1).setCellValue(book.getTitle());
                row.createCell(2).setCellValue(book.getAuthor());
                row.createCell(3).setCellValue(book.getPublisher());
                row.createCell(4).setCellValue(book.getPublishDate() != null ? book.getPublishDate().toString() : "");
                row.createCell(5).setCellValue(book.getStock());
                row.createCell(6).setCellValue(book.getAvailableStock());
                row.createCell(7).setCellValue(book.getBorrowedCount());
                row.createCell(8).setCellValue(book.getTotalBorrowCount());
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(out);
            return out.toByteArray();
        }
    }
    
    public List<Book> importBooks(InputStream inputStream) throws IOException {
        List<Book> books = new ArrayList<>();
        
        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            
            // 跳过标题行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                Book book = new Book();
                book.setIsbn(getCellValue(row.getCell(0)));
                book.setTitle(getCellValue(row.getCell(1)));
                book.setAuthor(getCellValue(row.getCell(2)));
                book.setPublisher(getCellValue(row.getCell(3)));
                
                String publishDateStr = getCellValue(row.getCell(4));
                if (publishDateStr != null && !publishDateStr.isEmpty()) {
                    try {
                        book.setPublishDate(LocalDate.parse(publishDateStr, DATE_FORMATTER));
                    } catch (Exception e) {
                        // 忽略日期解析错误
                    }
                }
                
                book.setStock(getNumericCellValue(row.getCell(5)));
                book.setAvailableStock(book.getStock()); // 初始可借数量等于库存
                book.setBorrowedCount(0);
                book.setTotalBorrowCount(0);
                
                books.add(book);
            }
        }
        
        return books;
    }
    
    public byte[] exportBorrowRecords(List<BorrowRecord> records) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("借阅记录");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"借阅人", "图书名称", "ISBN", "借阅时间", "应还时间", "实际归还时间", "状态", "续借次数"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                CellStyle style = workbook.createCellStyle();
                Font font = workbook.createFont();
                font.setBold(true);
                style.setFont(font);
                cell.setCellStyle(style);
            }
            
            // 填充数据
            int rowNum = 1;
            for (BorrowRecord record : records) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(record.getUser() != null ? record.getUser().getNickname() : "");
                row.createCell(1).setCellValue(record.getBook() != null ? record.getBook().getTitle() : "");
                row.createCell(2).setCellValue(record.getBook() != null ? record.getBook().getIsbn() : "");
                row.createCell(3).setCellValue(formatDateTime(record.getBorrowDate()));
                row.createCell(4).setCellValue(formatDateTime(record.getDueDate()));
                row.createCell(5).setCellValue(formatDateTime(record.getReturnDate()));
                row.createCell(6).setCellValue(getStatusText(record.getStatus()));
                row.createCell(7).setCellValue(record.getRenewCount());
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(out);
            return out.toByteArray();
        }
    }
    
    private String getCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DATE_FORMATTER.format(cell.getLocalDateTimeCellValue().toLocalDate());
                }
                return String.valueOf((int) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }
    
    private int getNumericCellValue(Cell cell) {
        if (cell == null) return 0;
        
        if (cell.getCellType() == CellType.NUMERIC) {
            return (int) cell.getNumericCellValue();
        }
        return 0;
    }
    
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMATTER) : "";
    }
    
    private String getStatusText(String status) {
        switch (status) {
            case "BORROWED":
                return "借阅中";
            case "RETURNED":
                return "已归还";
            case "OVERDUE":
                return "已逾期";
            default:
                return status;
        }
    }
}