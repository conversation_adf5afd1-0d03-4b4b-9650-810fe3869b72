<template>
  <div class="config-container">
    <el-card>
      <template #header>
        <span>系统设置</span>
      </template>
      
      <el-form :model="configForm" label-width="200px" style="max-width: 600px">
        <el-form-item v-for="config in configs" :key="config.id" :label="config.description">
          <el-input-number 
            v-if="isNumberConfig(config.configKey)"
            v-model="configForm[config.configKey]"
            :min="0"
            :max="getMaxValue(config.configKey)"
            @change="updateConfig(config)"
          />
          <el-switch
            v-else-if="isBooleanConfig(config.configKey)"
            v-model="configForm[config.configKey]"
            @change="updateConfig(config)"
          />
          <el-input
            v-else
            v-model="configForm[config.configKey]"
            @blur="updateConfig(config)"
          />
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>数据备份</span>
          <el-button type="primary" @click="createBackup">创建备份</el-button>
        </div>
      </template>
      
      <el-table :data="backups" v-loading="backupLoading">
        <el-table-column prop="name" label="备份文件" />
        <el-table-column label="创建时间" width="180">
          <template #default="scope">
            {{ parseBackupTime(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="restoreBackup(scope.row)" type="warning">恢复</el-button>
            <el-button size="small" @click="deleteBackup(scope.row)" type="danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { configApi } from '../api/config'
import api from '../api'

const configs = ref([])
const configForm = reactive({})
const backups = ref([])
const backupLoading = ref(false)

const numberConfigs = ['borrow_days', 'max_borrow_count', 'max_renew_count', 'renew_days', 'reservation_expire_days']
const booleanConfigs = ['allow_reservation', 'allow_renew']

const isNumberConfig = (key) => numberConfigs.includes(key)
const isBooleanConfig = (key) => booleanConfigs.includes(key)

const getMaxValue = (key) => {
  const maxValues = {
    'borrow_days': 365,
    'max_borrow_count': 20,
    'max_renew_count': 5,
    'renew_days': 30,
    'reservation_expire_days': 30
  }
  return maxValues[key] || 100
}

const loadConfigs = async () => {
  try {
    const res = await configApi.getAll()
    configs.value = res.data || []
    
    // 初始化表单值
    configs.value.forEach(config => {
      if (isNumberConfig(config.configKey)) {
        configForm[config.configKey] = parseInt(config.configValue)
      } else if (isBooleanConfig(config.configKey)) {
        configForm[config.configKey] = config.configValue === 'true'
      } else {
        configForm[config.configKey] = config.configValue
      }
    })
  } catch (error) {
    console.error(error)
  }
}

const updateConfig = async (config) => {
  try {
    let value = configForm[config.configKey]
    if (typeof value === 'boolean') {
      value = value.toString()
    } else if (typeof value === 'number') {
      value = value.toString()
    }
    
    config.configValue = value
    await configApi.update(config.id, config)
    ElMessage.success('配置更新成功')
  } catch (error) {
    console.error(error)
    loadConfigs() // 重新加载以恢复原值
  }
}

const loadBackups = async () => {
  backupLoading.value = true
  try {
    const res = await api.get('/backup/list')
    backups.value = res.data.map(name => ({ name }))
  } catch (error) {
    console.error(error)
  } finally {
    backupLoading.value = false
  }
}

const createBackup = async () => {
  try {
    await api.post('/backup/create')
    ElMessage.success('备份创建成功')
    loadBackups()
  } catch (error) {
    console.error(error)
  }
}

const restoreBackup = async (backup) => {
  try {
    await ElMessageBox.confirm('恢复备份将覆盖当前数据，确定要继续吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.post('/backup/restore', null, { params: { fileName: backup.name } })
    ElMessage.success('数据恢复成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm('确定要删除此备份吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.delete(`/backup/${backup.name}`)
    ElMessage.success('备份删除成功')
    loadBackups()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const parseBackupTime = (backup) => {
  // 从文件名解析时间 library_db_backup_20240101_120000.sql
  const match = backup.name.match(/(\d{8})_(\d{6})/)
  if (match) {
    const date = match[1]
    const time = match[2]
    return `${date.substr(0,4)}-${date.substr(4,2)}-${date.substr(6,2)} ${time.substr(0,2)}:${time.substr(2,2)}:${time.substr(4,2)}`
  }
  return ''
}

onMounted(() => {
  loadConfigs()
  loadBackups()
})
</script>

<style scoped>
.config-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>