package com.library.mapper;

import com.library.entity.User;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface UserMapper {
    
    @Select("SELECT * FROM sys_user WHERE username = #{username}")
    User findByUsername(String username);
    
    @Select("SELECT * FROM sys_user WHERE id = #{id}")
    User findById(Long id);
    
    @Select("SELECT * FROM sys_user")
    List<User> findAll();
    
    @Insert("INSERT INTO sys_user(username, password, nickname, role) " +
            "VALUES(#{username}, #{password}, #{nickname}, #{role})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
    
    @Update("UPDATE sys_user SET nickname = #{nickname}, role = #{role} WHERE id = #{id}")
    int update(User user);
    
    @Delete("DELETE FROM sys_user WHERE id = #{id}")
    int deleteById(Long id);
}