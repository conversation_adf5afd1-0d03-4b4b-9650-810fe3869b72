-- 创建数据库
CREATE DATABASE IF NOT EXISTS `library_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE `library_db`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `sys_user` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHAR(50) UNIQUE NOT NULL COMMENT '登录账号',
  `password` VARCHAR(100) NOT NULL COMMENT '加密密码',
  `nickname` VARCHAR(50) NOT NULL COMMENT '用户昵称',
  `role` ENUM('ADMIN','USER') DEFAULT 'USER' COMMENT '用户角色',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建图书表
CREATE TABLE IF NOT EXISTS `book` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `isbn` VARCHAR(20) UNIQUE NOT NULL COMMENT 'ISBN编号',
  `title` VARCHAR(100) NOT NULL COMMENT '书名',
  `author` VARCHAR(50) NOT NULL COMMENT '作者',
  `publisher` VARCHAR(50) COMMENT '出版社',
  `publish_date` DATE COMMENT '出版日期',
  `cover_url` VARCHAR(255) COMMENT '封面URL',
  `description` TEXT COMMENT '图书简介',
  `stock` INT DEFAULT 0 COMMENT '库存数量',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建分类表
CREATE TABLE IF NOT EXISTS `category` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) UNIQUE NOT NULL COMMENT '分类名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建图书分类关系表
CREATE TABLE IF NOT EXISTS `book_category` (
  `book_id` INT NOT NULL,
  `category_id` INT NOT NULL,
  PRIMARY KEY (`book_id`, `category_id`),
  FOREIGN KEY (`book_id`) REFERENCES `book`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `category`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入用户数据（密码均为123456的BCrypt加密值）
INSERT INTO `sys_user` (username, password, nickname, role) VALUES
('admin', '$2a$10$Y9s6w3Uu5J7H0Z1f0z0K.OU0d5f3w3b5v7X9yV2bB1c3d4v5n6m7', '图书管理员', 'ADMIN'),
('lisa', '$2a$10$Y9s6w3Uu5J7H0Z1f0z0K.OU0d5f3w3b5v7X9yV2bB1c3d4v5n6m7', '借阅专员', 'USER'),
('mike', '$2a$10$Y9s6w3Uu5J7H0Z1f0z0K.OU0d5f3w3b5v7X9yV2bB1c3d4v5n6m7', '读者服务', 'USER');

-- 插入图书分类数据
INSERT INTO `category` (name) VALUES
('计算机科学'), ('文学小说'), ('历史传记'), 
('经济管理'), ('艺术设计'), ('科普读物'),
('心理学'), ('教育学习'), ('医学健康');

-- 插入图书数据
INSERT INTO `book` (isbn, title, author, publisher, publish_date, cover_url, description, stock) VALUES
('9787111544937', '深入理解Java虚拟机', '周志明', '机械工业出版社', '2019-12-01', 'https://cover.com/jvm.jpg', 'Java虚拟机原理深度解析', 15),
('9787544281097', '三体全集', '刘慈欣', '重庆出版社', '2016-06-01', 'https://cover.com/three-body.jpg', '中国科幻里程碑作品', 22),
('9787108063106', '人类简史', '尤瓦尔·赫拉利', '中信出版社', '2017-02-01', 'https://cover.com/sapiens.jpg', '从动物到上帝的人类发展史', 18),
('9787550289374', '算法图解', 'Aditya Bhargava', '人民邮电出版社', '2020-05-01', 'https://cover.com/algo.jpg', '像小说一样有趣的算法入门书', 12),
('9787508684031', '经济学原理', '曼昆', '中信出版社', '2018-03-15', 'https://cover.com/economics.jpg', '经典经济学教材', 9),
('9787550254075', '活着', '余华', '作家出版社', '2017-08-01', 'https://cover.com/alive.jpg', '一部关于生存的经典小说', 25),
('9787513348365', 'Spring Boot实战', 'Craig Walls', '人民邮电出版社', '2021-04-01', 'https://cover.com/springboot.jpg', 'Spring Boot开发权威指南', 8),
('9787559633179', 'Vue.js设计与实现', '霍春阳', '电子工业出版社', '2022-01-01', 'https://cover.com/vue.jpg', '深入解析Vue.js框架原理', 11),
('9787505742478', '时间简史', '史蒂芬·霍金', '湖南科技出版社', '2019-05-01', 'https://cover.com/time.jpg', '探索宇宙奥秘的科普经典', 7),
('9787547737995', '百年孤独', '马尔克斯', '南海出版公司', '2017-06-01', 'https://cover.com/solitude.jpg', '魔幻现实主义文学代表作', 20),
('9787559627802', '明朝那些事儿', '当年明月', '浙江人民出版社', '2020-09-01', 'https://cover.com/ming.jpg', '通俗有趣的历史读物', 30),
('9787559446786', 'Python编程从入门到实践', 'Eric Matthes', '人民邮电出版社', '2020-07-01', 'https://cover.com/python.jpg', 'Python学习最佳实践', 14),
('9787521733679', '设计心理学', '唐纳德·诺曼', '中信出版社', '2021-11-01', 'https://cover.com/design-psy.jpg', '产品设计的心理学原理', 6),
('9787540487640', '非暴力沟通', '马歇尔·卢森堡', '华夏出版社', '2018-12-01', 'https://cover.com/communication.jpg', '改善人际沟通的经典著作', 17),
('9787559639331', '黑客与画家', 'Paul Graham', '人民邮电出版社', '2022-03-01', 'https://cover.com/hacker.jpg', '硅谷创业教父的思考集', 10),
('9787559654181', '代码整洁之道', 'Robert C. Martin', '人民邮电出版社', '2020-08-01', 'https://cover.com/clean-code.jpg', '编写高质量代码的实践指南', 13),
('9787111701043', '计算机网络', 'Andrew S. Tanenbaum', '机械工业出版社', '2021-09-01', 'https://cover.com/network.jpg', '计算机网络的经典教材', 5),
('9787521740011', '原则', '瑞·达利欧', '中信出版社', '2018-01-01', 'https://cover.com/principles.jpg', '投资大师的人生经验总结', 19),
('9787559648357', 'MySQL必知必会', 'Ben Forta', '人民邮电出版社', '2021-11-01', 'https://cover.com/mysql.jpg', 'MySQL快速入门指南', 16),
('9787513927741', '高效能人士的七个习惯', '史蒂芬·柯维', '中国青年出版社', '2020-10-01', 'https://cover.com/habits.jpg', '个人管理的经典著作', 23);