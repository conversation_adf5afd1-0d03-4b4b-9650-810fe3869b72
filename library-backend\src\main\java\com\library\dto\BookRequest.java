package com.library.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class BookRequest {
    private Long id;
    
    @NotBlank(message = "ISBN不能为空")
    private String isbn;
    
    @NotBlank(message = "书名不能为空")
    private String title;
    
    @NotBlank(message = "作者不能为空")
    private String author;
    
    private String publisher;
    private LocalDate publishDate;
    private String coverUrl;
    private String description;
    
    @NotNull(message = "库存不能为空")
    private Integer stock;
    
    private List<Long> categoryIds;
}