package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.SystemConfig;
import com.library.service.SystemConfigService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/config")
@CrossOrigin
public class SystemConfigController {
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    @GetMapping
    public ApiResponse<List<SystemConfig>> getAllConfigs(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(systemConfigService.findAll());
    }
    
    @PutMapping("/{id}")
    public ApiResponse<String> updateConfig(@PathVariable Long id, @RequestBody SystemConfig config, 
                                            HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        config.setId(id);
        if (systemConfigService.update(config)) {
            return ApiResponse.success("更新成功", null);
        }
        return ApiResponse.error("更新失败");
    }
}