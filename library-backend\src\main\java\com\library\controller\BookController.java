package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.dto.BookRequest;
import com.library.entity.Book;
import com.library.service.BookService;
import jakarta.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/books")
@CrossOrigin
public class BookController {
    
    @Autowired
    private BookService bookService;
    
    @GetMapping
    public ApiResponse<List<Book>> getAllBooks() {
        List<Book> books = bookService.findAll();
        return ApiResponse.success(books);
    }
    
    @GetMapping("/{id}")
    public ApiResponse<Book> getBookById(@PathVariable Long id) {
        Book book = bookService.findById(id);
        if (book == null) {
            return ApiResponse.error("图书不存在");
        }
        return ApiResponse.success(book);
    }
    
    @PostMapping
    public ApiResponse<String> createBook(@Valid @RequestBody BookRequest request) {
        Book book = new Book();
        BeanUtils.copyProperties(request, book);
        
        if (bookService.create(book, request.getCategoryIds())) {
            return ApiResponse.success("创建成功", null);
        }
        return ApiResponse.error("ISBN已存在");
    }
    
    @PutMapping("/{id}")
    public ApiResponse<String> updateBook(@PathVariable Long id, @Valid @RequestBody BookRequest request) {
        Book book = new Book();
        BeanUtils.copyProperties(request, book);
        book.setId(id);
        
        if (bookService.update(book, request.getCategoryIds())) {
            return ApiResponse.success("更新成功", null);
        }
        return ApiResponse.error("更新失败");
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteBook(@PathVariable Long id) {
        if (bookService.deleteById(id)) {
            return ApiResponse.success("删除成功", null);
        }
        return ApiResponse.error("删除失败");
    }
}