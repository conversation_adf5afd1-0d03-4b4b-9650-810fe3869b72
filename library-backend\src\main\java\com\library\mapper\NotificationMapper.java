package com.library.mapper;

import com.library.entity.Notification;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface NotificationMapper {
    
    @Select("SELECT * FROM notification WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Notification> findByUserId(Long userId);
    
    @Select("SELECT * FROM notification WHERE user_id = #{userId} AND is_read = false ORDER BY create_time DESC")
    List<Notification> findUnreadByUserId(Long userId);
    
    @Select("SELECT COUNT(*) FROM notification WHERE user_id = #{userId} AND is_read = false")
    int countUnreadByUserId(Long userId);
    
    @Insert("INSERT INTO notification(user_id, title, content, type) " +
            "VALUES(#{userId}, #{title}, #{content}, #{type})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Notification notification);
    
    @Update("UPDATE notification SET is_read = true WHERE id = #{id}")
    int markAsRead(Long id);
    
    @Update("UPDATE notification SET is_read = true WHERE user_id = #{userId}")
    int markAllAsRead(Long userId);
    
    @Delete("DELETE FROM notification WHERE id = #{id}")
    int deleteById(Long id);
}