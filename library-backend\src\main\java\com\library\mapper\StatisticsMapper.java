package com.library.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface StatisticsMapper {
    
    // 借阅统计
    @Select("SELECT COUNT(*) as count, DATE(borrow_date) as date " +
            "FROM borrow_record " +
            "WHERE borrow_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(borrow_date) " +
            "ORDER BY date")
    List<Map<String, Object>> getBorrowStatsByDate(@Param("startDate") LocalDateTime startDate, 
                                                    @Param("endDate") LocalDateTime endDate);
    
    // 热门图书排行
    @Select("SELECT b.id, b.title, b.author, b.total_borrow_count as borrowCount " +
            "FROM book b " +
            "WHERE b.total_borrow_count > 0 " +
            "ORDER BY b.total_borrow_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getTopBorrowedBooks(@Param("limit") int limit);
    
    // 活跃读者排行
    @Select("SELECT u.id, u.nickname, COUNT(br.id) as borrowCount " +
            "FROM sys_user u " +
            "JOIN borrow_record br ON u.id = br.user_id " +
            "WHERE br.borrow_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY u.id, u.nickname " +
            "ORDER BY borrowCount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getTopActiveUsers(@Param("startDate") LocalDateTime startDate, 
                                                @Param("endDate") LocalDateTime endDate, 
                                                @Param("limit") int limit);
    
    // 分类借阅统计
    @Select("SELECT c.name as category, COUNT(br.id) as borrowCount " +
            "FROM category c " +
            "JOIN book_category bc ON c.id = bc.category_id " +
            "JOIN book b ON bc.book_id = b.id " +
            "JOIN borrow_record br ON b.id = br.book_id " +
            "WHERE br.borrow_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY c.id, c.name " +
            "ORDER BY borrowCount DESC")
    List<Map<String, Object>> getCategoryBorrowStats(@Param("startDate") LocalDateTime startDate, 
                                                      @Param("endDate") LocalDateTime endDate);
    
    // 概览统计
    @Select("SELECT " +
            "(SELECT COUNT(*) FROM book) as totalBooks, " +
            "(SELECT COUNT(*) FROM sys_user) as totalUsers, " +
            "(SELECT COUNT(*) FROM borrow_record WHERE status = 'BORROWED') as activeBorrows, " +
            "(SELECT COUNT(*) FROM borrow_record WHERE status = 'OVERDUE') as overdueCount, " +
            "(SELECT COUNT(*) FROM borrow_record WHERE DATE(borrow_date) = CURDATE()) as todayBorrows, " +
            "(SELECT COUNT(*) FROM borrow_record WHERE DATE(return_date) = CURDATE()) as todayReturns")
    Map<String, Object> getOverviewStats();
    
    // 月度借阅趋势
    @Select("SELECT DATE_FORMAT(borrow_date, '%Y-%m') as month, COUNT(*) as count " +
            "FROM borrow_record " +
            "WHERE borrow_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH) " +
            "GROUP BY DATE_FORMAT(borrow_date, '%Y-%m') " +
            "ORDER BY month")
    List<Map<String, Object>> getMonthlyBorrowTrend();
    
    // 图书库存预警
    @Select("SELECT id, title, author, stock, available_stock " +
            "FROM book " +
            "WHERE available_stock <= #{threshold} " +
            "ORDER BY available_stock, title")
    List<Map<String, Object>> getLowStockBooks(@Param("threshold") int threshold);
}