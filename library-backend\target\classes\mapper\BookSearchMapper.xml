<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.library.mapper.BookSearchMapper">
    
    <resultMap id="BookResultMap" type="com.library.entity.Book">
        <id property="id" column="id"/>
        <result property="isbn" column="isbn"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="publisher" column="publisher"/>
        <result property="publishDate" column="publish_date"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="description" column="description"/>
        <result property="stock" column="stock"/>
        <result property="availableStock" column="available_stock"/>
        <result property="borrowedCount" column="borrowed_count"/>
        <result property="totalBorrowCount" column="total_borrow_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <collection property="categories" ofType="com.library.entity.Category">
            <id property="id" column="category_id"/>
            <result property="name" column="category_name"/>
        </collection>
    </resultMap>
    
    <select id="searchBooks" resultMap="BookResultMap">
        SELECT DISTINCT
            b.*,
            c.id as category_id,
            c.name as category_name
        FROM book b
        LEFT JOIN book_category bc ON b.id = bc.book_id
        LEFT JOIN category c ON bc.category_id = c.id
        <where>
            <if test="req.keyword != null and req.keyword != ''">
                AND (b.title LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.author LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.publisher LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.isbn LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.description LIKE CONCAT('%', #{req.keyword}, '%'))
            </if>
            <if test="req.title != null and req.title != ''">
                AND b.title LIKE CONCAT('%', #{req.title}, '%')
            </if>
            <if test="req.author != null and req.author != ''">
                AND b.author LIKE CONCAT('%', #{req.author}, '%')
            </if>
            <if test="req.publisher != null and req.publisher != ''">
                AND b.publisher LIKE CONCAT('%', #{req.publisher}, '%')
            </if>
            <if test="req.isbn != null and req.isbn != ''">
                AND b.isbn = #{req.isbn}
            </if>
            <if test="req.publishDateStart != null">
                AND b.publish_date >= #{req.publishDateStart}
            </if>
            <if test="req.publishDateEnd != null">
                AND b.publish_date &lt;= #{req.publishDateEnd}
            </if>
            <if test="req.stockMin != null">
                AND b.stock >= #{req.stockMin}
            </if>
            <if test="req.stockMax != null">
                AND b.stock &lt;= #{req.stockMax}
            </if>
            <if test="req.onlyAvailable != null and req.onlyAvailable">
                AND b.available_stock > 0
            </if>
            <if test="req.categoryIds != null and req.categoryIds.size() > 0">
                AND b.id IN (
                    SELECT DISTINCT book_id FROM book_category 
                    WHERE category_id IN
                    <foreach collection="req.categoryIds" item="categoryId" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                )
            </if>
        </where>
        <if test="req.sortBy != null and req.sortBy != ''">
            ORDER BY
            <choose>
                <when test="req.sortBy == 'title'">b.title</when>
                <when test="req.sortBy == 'author'">b.author</when>
                <when test="req.sortBy == 'publishDate'">b.publish_date</when>
                <when test="req.sortBy == 'stock'">b.stock</when>
                <when test="req.sortBy == 'borrowCount'">b.total_borrow_count</when>
                <otherwise>b.id</otherwise>
            </choose>
            <if test="req.sortOrder != null and req.sortOrder == 'DESC'">
                DESC
            </if>
            <if test="req.sortOrder == null or req.sortOrder != 'DESC'">
                ASC
            </if>
        </if>
        LIMIT #{req.pageSize} OFFSET #{req.pageSize} * (#{req.page} - 1)
    </select>
    
    <select id="countSearchBooks" resultType="int">
        SELECT COUNT(DISTINCT b.id)
        FROM book b
        LEFT JOIN book_category bc ON b.id = bc.book_id
        <where>
            <if test="req.keyword != null and req.keyword != ''">
                AND (b.title LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.author LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.publisher LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.isbn LIKE CONCAT('%', #{req.keyword}, '%')
                    OR b.description LIKE CONCAT('%', #{req.keyword}, '%'))
            </if>
            <if test="req.title != null and req.title != ''">
                AND b.title LIKE CONCAT('%', #{req.title}, '%')
            </if>
            <if test="req.author != null and req.author != ''">
                AND b.author LIKE CONCAT('%', #{req.author}, '%')
            </if>
            <if test="req.publisher != null and req.publisher != ''">
                AND b.publisher LIKE CONCAT('%', #{req.publisher}, '%')
            </if>
            <if test="req.isbn != null and req.isbn != ''">
                AND b.isbn = #{req.isbn}
            </if>
            <if test="req.publishDateStart != null">
                AND b.publish_date >= #{req.publishDateStart}
            </if>
            <if test="req.publishDateEnd != null">
                AND b.publish_date &lt;= #{req.publishDateEnd}
            </if>
            <if test="req.stockMin != null">
                AND b.stock >= #{req.stockMin}
            </if>
            <if test="req.stockMax != null">
                AND b.stock &lt;= #{req.stockMax}
            </if>
            <if test="req.onlyAvailable != null and req.onlyAvailable">
                AND b.available_stock > 0
            </if>
            <if test="req.categoryIds != null and req.categoryIds.size() > 0">
                AND b.id IN (
                    SELECT DISTINCT book_id FROM book_category 
                    WHERE category_id IN
                    <foreach collection="req.categoryIds" item="categoryId" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
                )
            </if>
        </where>
    </select>
</mapper>