package com.library.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class BackupService {
    
    @Value("${spring.datasource.url}")
    private String dbUrl;
    
    @Value("${spring.datasource.username}")
    private String dbUsername;
    
    @Value("${spring.datasource.password}")
    private String dbPassword;
    
    private static final String BACKUP_DIR = "backups";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    public String backupDatabase() throws IOException, InterruptedException {
        // 创建备份目录
        Path backupPath = Paths.get(BACKUP_DIR);
        if (!Files.exists(backupPath)) {
            Files.createDirectories(backupPath);
        }
        
        // 解析数据库名称
        String dbName = extractDatabaseName(dbUrl);
        
        // 生成备份文件名
        String timestamp = LocalDateTime.now().format(formatter);
        String backupFileName = String.format("%s_backup_%s.sql", dbName, timestamp);
        String backupFilePath = Paths.get(BACKUP_DIR, backupFileName).toString();
        
        // 构建mysqldump命令
        List<String> command = new ArrayList<>();
        command.add("mysqldump");
        command.add("-u" + dbUsername);
        if (dbPassword != null && !dbPassword.isEmpty()) {
            command.add("-p" + dbPassword);
        }
        command.add("--databases");
        command.add(dbName);
        command.add("--result-file=" + backupFilePath);
        
        // 执行备份
        ProcessBuilder pb = new ProcessBuilder(command);
        Process process = pb.start();
        
        // 读取输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        String line;
        StringBuilder output = new StringBuilder();
        while ((line = reader.readLine()) != null) {
            output.append(line).append("\n");
        }
        
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("备份失败: " + output.toString());
        }
        
        return backupFilePath;
    }
    
    public void restoreDatabase(String backupFilePath) throws IOException, InterruptedException {
        File backupFile = new File(backupFilePath);
        if (!backupFile.exists()) {
            throw new RuntimeException("备份文件不存在: " + backupFilePath);
        }
        
        // 构建mysql命令
        List<String> command = new ArrayList<>();
        command.add("mysql");
        command.add("-u" + dbUsername);
        if (dbPassword != null && !dbPassword.isEmpty()) {
            command.add("-p" + dbPassword);
        }
        
        // 执行恢复
        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectInput(backupFile);
        Process process = pb.start();
        
        // 读取输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        String line;
        StringBuilder output = new StringBuilder();
        while ((line = reader.readLine()) != null) {
            output.append(line).append("\n");
        }
        
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("恢复失败: " + output.toString());
        }
    }
    
    public List<String> listBackups() throws IOException {
        Path backupPath = Paths.get(BACKUP_DIR);
        List<String> backups = new ArrayList<>();
        
        if (Files.exists(backupPath)) {
            Files.list(backupPath)
                    .filter(path -> path.toString().endsWith(".sql"))
                    .forEach(path -> backups.add(path.getFileName().toString()));
        }
        
        return backups;
    }
    
    public void deleteBackup(String fileName) throws IOException {
        Path filePath = Paths.get(BACKUP_DIR, fileName);
        if (Files.exists(filePath)) {
            Files.delete(filePath);
        }
    }
    
    private String extractDatabaseName(String url) {
        // 从URL中提取数据库名称
        // 例如: **************************************?...
        String[] parts = url.split("/");
        if (parts.length > 3) {
            String dbPart = parts[3];
            int queryIndex = dbPart.indexOf('?');
            if (queryIndex > 0) {
                return dbPart.substring(0, queryIndex);
            }
            return dbPart;
        }
        return "library_db";
    }
}