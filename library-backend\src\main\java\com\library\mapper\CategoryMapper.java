package com.library.mapper;

import com.library.entity.Category;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CategoryMapper {
    
    @Select("SELECT * FROM category WHERE id = #{id}")
    Category findById(Long id);
    
    @Select("SELECT * FROM category WHERE name = #{name}")
    Category findByName(String name);
    
    @Select("SELECT * FROM category")
    List<Category> findAll();
    
    @Select("SELECT c.* FROM category c " +
            "JOIN book_category bc ON c.id = bc.category_id " +
            "WHERE bc.book_id = #{bookId}")
    List<Category> findCategoriesByBookId(Long bookId);
    
    @Insert("INSERT INTO category(name) VALUES(#{name})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Category category);
    
    @Update("UPDATE category SET name = #{name} WHERE id = #{id}")
    int update(Category category);
    
    @Delete("DELETE FROM category WHERE id = #{id}")
    int deleteById(Long id);
}