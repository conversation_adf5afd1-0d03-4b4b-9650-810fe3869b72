import api from './index'

export const bookApi = {
  getAll() {
    return api.get('/books')
  },
  
  getById(id) {
    return api.get(`/books/${id}`)
  },
  
  create(data) {
    return api.post('/books', data)
  },
  
  update(id, data) {
    return api.put(`/books/${id}`, data)
  },
  
  delete(id) {
    return api.delete(`/books/${id}`)
  },
  
  // 高级搜索
  search(searchParams) {
    return api.post('/books/search', searchParams)
  },
  
  // 导出图书列表
  exportBooks() {
    return api.get('/export/books', { responseType: 'blob' })
  },
  
  // 导入图书
  importBooks(file) {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/export/import/books', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}