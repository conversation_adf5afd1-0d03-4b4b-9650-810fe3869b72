package com.library.entity;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class Book {
    private Long id;
    private String isbn;
    private String title;
    private String author;
    private String publisher;
    private LocalDate publishDate;
    private String coverUrl;
    private String description;
    private Integer stock;
    private Integer availableStock;
    private Integer borrowedCount;
    private Integer totalBorrowCount;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private List<Category> categories;
}