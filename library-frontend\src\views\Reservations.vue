<template>
  <div class="reservations-container">
    <el-card>
      <template #header>
        <span>我的预约</span>
      </template>
      
      <el-table :data="reservations" v-loading="loading" style="width: 100%">
        <el-table-column prop="book.title" label="书名" />
        <el-table-column prop="book.author" label="作者" width="120" />
        <el-table-column prop="reservationDate" label="预约时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.reservationDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expireDate" label="过期时间" width="180">
          <template #default="scope">
            <span v-if="scope.row.expireDate">
              {{ formatDateTime(scope.row.expireDate) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'WAITING'"
              size="small" 
              type="danger" 
              @click="handleCancel(scope.row)"
            >
              取消预约
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-empty v-if="!loading && reservations.length === 0" description="暂无预约记录" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reservationApi } from '../api/reservation'
import dayjs from 'dayjs'

const loading = ref(false)
const reservations = ref([])

const formatDateTime = (datetime) => {
  return datetime ? dayjs(datetime).format('YYYY-MM-DD HH:mm') : ''
}

const getStatusType = (status) => {
  const types = {
    'WAITING': 'info',
    'NOTIFIED': 'warning',
    'CANCELLED': 'danger',
    'FULFILLED': 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'WAITING': '等待中',
    'NOTIFIED': '已通知',
    'CANCELLED': '已取消',
    'FULFILLED': '已完成'
  }
  return texts[status] || status
}

const loadReservations = async () => {
  loading.value = true
  try {
    const res = await reservationApi.getMyReservations()
    reservations.value = res.data || []
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleCancel = async (reservation) => {
  try {
    await ElMessageBox.confirm('确定要取消这个预约吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await reservationApi.cancelReservation(reservation.id)
    ElMessage.success('预约已取消')
    loadReservations()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

onMounted(() => {
  loadReservations()
})
</script>

<style scoped>
.reservations-container {
  padding: 20px;
}
</style>