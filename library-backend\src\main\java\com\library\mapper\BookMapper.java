package com.library.mapper;

import com.library.entity.Book;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface BookMapper {
    
    @Select("SELECT * FROM book WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "categories", column = "id",
                many = @Many(select = "com.library.mapper.CategoryMapper.findCategoriesByBookId"))
    })
    Book findById(Long id);
    
    @Select("SELECT * FROM book")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "categories", column = "id",
                many = @Many(select = "com.library.mapper.CategoryMapper.findCategoriesByBookId"))
    })
    List<Book> findAll();
    
    @Select("SELECT * FROM book WHERE isbn = #{isbn}")
    Book findByIsbn(String isbn);
    
    @Insert("INSERT INTO book(isbn, title, author, publisher, publish_date, cover_url, description, stock) " +
            "VALUES(#{isbn}, #{title}, #{author}, #{publisher}, #{publishDate}, #{coverUrl}, #{description}, #{stock})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Book book);
    
    @Update("UPDATE book SET title = #{title}, author = #{author}, publisher = #{publisher}, " +
            "publish_date = #{publishDate}, cover_url = #{coverUrl}, description = #{description}, " +
            "stock = #{stock}, available_stock = #{availableStock}, borrowed_count = #{borrowedCount}, " +
            "total_borrow_count = #{totalBorrowCount} WHERE id = #{id}")
    int update(Book book);
    
    @Update("UPDATE book SET available_stock = available_stock - 1, borrowed_count = borrowed_count + 1, " +
            "total_borrow_count = total_borrow_count + 1 WHERE id = #{id} AND available_stock > 0")
    int borrowBook(Long id);
    
    @Update("UPDATE book SET available_stock = available_stock + 1, borrowed_count = borrowed_count - 1 WHERE id = #{id}")
    int returnBook(Long id);
    
    @Delete("DELETE FROM book WHERE id = #{id}")
    int deleteById(Long id);
    
    @Insert("INSERT INTO book_category(book_id, category_id) VALUES(#{bookId}, #{categoryId})")
    int insertBookCategory(@Param("bookId") Long bookId, @Param("categoryId") Long categoryId);
    
    @Delete("DELETE FROM book_category WHERE book_id = #{bookId}")
    int deleteBookCategories(Long bookId);
}