package com.library.service;

import com.library.entity.Category;
import com.library.mapper.CategoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CategoryService {
    
    @Autowired
    private CategoryMapper categoryMapper;
    
    public Category findById(Long id) {
        return categoryMapper.findById(id);
    }
    
    public List<Category> findAll() {
        return categoryMapper.findAll();
    }
    
    public boolean create(Category category) {
        // 检查分类名是否已存在
        if (categoryMapper.findByName(category.getName()) != null) {
            return false;
        }
        return categoryMapper.insert(category) > 0;
    }
    
    public boolean update(Category category) {
        // 检查分类名是否已被其他分类使用
        Category existing = categoryMapper.findByName(category.getName());
        if (existing != null && !existing.getId().equals(category.getId())) {
            return false;
        }
        return categoryMapper.update(category) > 0;
    }
    
    public boolean deleteById(Long id) {
        return categoryMapper.deleteById(id) > 0;
    }
}