package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.Notification;
import com.library.service.NotificationService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/notification")
@CrossOrigin
public class NotificationController {
    
    @Autowired
    private NotificationService notificationService;
    
    @GetMapping("/my")
    public ApiResponse<Map<String, Object>> getMyNotifications(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        
        List<Notification> notifications = notificationService.findByUserId(userId);
        int unreadCount = notificationService.countUnreadByUserId(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("notifications", notifications);
        result.put("unreadCount", unreadCount);
        
        return ApiResponse.success(result);
    }
    
    @GetMapping("/unread")
    public ApiResponse<List<Notification>> getUnreadNotifications(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        return ApiResponse.success(notificationService.findUnreadByUserId(userId));
    }
    
    @PostMapping("/read/{id}")
    public ApiResponse<String> markAsRead(@PathVariable Long id) {
        if (notificationService.markAsRead(id)) {
            return ApiResponse.success("标记成功", null);
        }
        return ApiResponse.error("标记失败");
    }
    
    @PostMapping("/read-all")
    public ApiResponse<String> markAllAsRead(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        
        if (notificationService.markAllAsRead(userId)) {
            return ApiResponse.success("全部标记成功", null);
        }
        return ApiResponse.error("标记失败");
    }
}