package com.library.mapper;

import com.library.entity.BorrowRecord;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface BorrowRecordMapper {
    
    @Select("SELECT * FROM borrow_record WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "bookId", column = "book_id"),
        @Result(property = "user", column = "user_id",
                one = @One(select = "com.library.mapper.UserMapper.findById")),
        @Result(property = "book", column = "book_id",
                one = @One(select = "com.library.mapper.BookMapper.findById"))
    })
    BorrowRecord findById(Long id);
    
    @Select("SELECT * FROM borrow_record WHERE user_id = #{userId} ORDER BY borrow_date DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "bookId", column = "book_id"),
        @Result(property = "user", column = "user_id",
                one = @One(select = "com.library.mapper.UserMapper.findById")),
        @Result(property = "book", column = "book_id",
                one = @One(select = "com.library.mapper.BookMapper.findById"))
    })
    List<BorrowRecord> findByUserId(Long userId);
    
    @Select("SELECT * FROM borrow_record WHERE book_id = #{bookId} AND status = 'BORROWED'")
    List<BorrowRecord> findActiveByBookId(Long bookId);
    
    @Select("SELECT * FROM borrow_record WHERE user_id = #{userId} AND status = 'BORROWED'")
    List<BorrowRecord> findActiveByUserId(Long userId);
    
    @Select("SELECT * FROM borrow_record WHERE status = #{status} ORDER BY borrow_date DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "bookId", column = "book_id"),
        @Result(property = "user", column = "user_id",
                one = @One(select = "com.library.mapper.UserMapper.findById")),
        @Result(property = "book", column = "book_id",
                one = @One(select = "com.library.mapper.BookMapper.findById"))
    })
    List<BorrowRecord> findByStatus(String status);
    
    @Select("SELECT * FROM borrow_record ORDER BY borrow_date DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "bookId", column = "book_id"),
        @Result(property = "user", column = "user_id",
                one = @One(select = "com.library.mapper.UserMapper.findById")),
        @Result(property = "book", column = "book_id",
                one = @One(select = "com.library.mapper.BookMapper.findById"))
    })
    List<BorrowRecord> findAll();
    
    @Select("SELECT COUNT(*) FROM borrow_record WHERE user_id = #{userId} AND status = 'BORROWED'")
    int countActiveByUserId(Long userId);
    
    @Insert("INSERT INTO borrow_record(user_id, book_id, due_date, status) " +
            "VALUES(#{userId}, #{bookId}, #{dueDate}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(BorrowRecord record);
    
    @Update("UPDATE borrow_record SET return_date = #{returnDate}, status = #{status} WHERE id = #{id}")
    int updateReturn(BorrowRecord record);
    
    @Update("UPDATE borrow_record SET due_date = #{dueDate}, renew_count = #{renewCount} WHERE id = #{id}")
    int updateRenew(BorrowRecord record);
    
    @Update("UPDATE borrow_record SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status);
    
    @Select("SELECT * FROM borrow_record WHERE status = 'BORROWED' AND due_date < NOW()")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "bookId", column = "book_id"),
        @Result(property = "user", column = "user_id",
                one = @One(select = "com.library.mapper.UserMapper.findById")),
        @Result(property = "book", column = "book_id",
                one = @One(select = "com.library.mapper.BookMapper.findById"))
    })
    List<BorrowRecord> findOverdueRecords();
}