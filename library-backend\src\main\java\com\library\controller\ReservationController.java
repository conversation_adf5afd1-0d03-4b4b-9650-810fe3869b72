package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.Reservation;
import com.library.service.ReservationService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/reservation")
@CrossOrigin
public class ReservationController {
    
    @Autowired
    private ReservationService reservationService;
    
    @GetMapping("/my")
    public ApiResponse<List<Reservation>> getMyReservations(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        return ApiResponse.success(reservationService.findByUserId(userId));
    }
    
    @PostMapping("/create")
    public ApiResponse<String> createReservation(@RequestBody Map<String, Long> params, HttpServletRequest request) {
        Long bookId = params.get("bookId");
        Long userId = (Long) request.getAttribute("userId");
        
        String error = reservationService.createReservation(userId, bookId);
        if (error == null) {
            return ApiResponse.success("预约成功", null);
        }
        return ApiResponse.error(error);
    }
    
    @PostMapping("/cancel/{id}")
    public ApiResponse<String> cancelReservation(@PathVariable Long id) {
        String error = reservationService.cancelReservation(id);
        if (error == null) {
            return ApiResponse.success("取消成功", null);
        }
        return ApiResponse.error(error);
    }
}