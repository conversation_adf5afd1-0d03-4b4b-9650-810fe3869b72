import api from './index'

export const statisticsApi = {
  // 获取概览统计
  getOverview() {
    return api.get('/statistics/overview')
  },
  
  // 获取综合统计报表
  getComprehensive() {
    return api.get('/statistics/comprehensive')
  },
  
  // 获取借阅趋势
  getBorrowTrend(startDate, endDate) {
    return api.get('/statistics/borrow-trend', {
      params: { startDate, endDate }
    })
  },
  
  // 获取热门图书
  getTopBooks(limit = 10) {
    return api.get('/statistics/top-books', {
      params: { limit }
    })
  },
  
  // 获取活跃用户
  getTopUsers(startDate, endDate, limit = 10) {
    return api.get('/statistics/top-users', {
      params: { startDate, endDate, limit }
    })
  },
  
  // 获取分类统计
  getCategoryStats(startDate, endDate) {
    return api.get('/statistics/category-stats', {
      params: { startDate, endDate }
    })
  },
  
  // 获取月度趋势
  getMonthlyTrend() {
    return api.get('/statistics/monthly-trend')
  }
}