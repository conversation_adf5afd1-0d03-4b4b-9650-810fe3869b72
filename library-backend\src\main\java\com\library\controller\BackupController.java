package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.service.BackupService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/backup")
@CrossOrigin
public class BackupController {
    
    @Autowired
    private BackupService backupService;
    
    @PostMapping("/create")
    public ApiResponse<String> createBackup(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        try {
            String backupPath = backupService.backupDatabase();
            return ApiResponse.success("备份成功", backupPath);
        } catch (Exception e) {
            return ApiResponse.error("备份失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/restore")
    public ApiResponse<String> restoreBackup(@RequestParam String fileName, HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        try {
            backupService.restoreDatabase("backups/" + fileName);
            return ApiResponse.success("恢复成功", null);
        } catch (Exception e) {
            return ApiResponse.error("恢复失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/list")
    public ApiResponse<List<String>> listBackups(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        try {
            List<String> backups = backupService.listBackups();
            return ApiResponse.success(backups);
        } catch (IOException e) {
            return ApiResponse.error("获取备份列表失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/{fileName}")
    public ApiResponse<String> deleteBackup(@PathVariable String fileName, HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        
        try {
            backupService.deleteBackup(fileName);
            return ApiResponse.success("删除成功", null);
        } catch (IOException e) {
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }
}