package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.Category;
import com.library.service.CategoryService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/categories")
@CrossOrigin
public class CategoryController {
    
    @Autowired
    private CategoryService categoryService;
    
    @GetMapping
    public ApiResponse<List<Category>> getAllCategories() {
        List<Category> categories = categoryService.findAll();
        return ApiResponse.success(categories);
    }
    
    @GetMapping("/{id}")
    public ApiResponse<Category> getCategoryById(@PathVariable Long id) {
        Category category = categoryService.findById(id);
        if (category == null) {
            return ApiResponse.error("分类不存在");
        }
        return ApiResponse.success(category);
    }
    
    @PostMapping
    public ApiResponse<String> createCategory(@Valid @RequestBody Category category) {
        if (categoryService.create(category)) {
            return ApiResponse.success("创建成功", null);
        }
        return ApiResponse.error("分类名称已存在");
    }
    
    @PutMapping("/{id}")
    public ApiResponse<String> updateCategory(@PathVariable Long id, @Valid @RequestBody Category category) {
        category.setId(id);
        if (categoryService.update(category)) {
            return ApiResponse.success("更新成功", null);
        }
        return ApiResponse.error("分类名称已存在");
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteCategory(@PathVariable Long id) {
        if (categoryService.deleteById(id)) {
            return ApiResponse.success("删除成功", null);
        }
        return ApiResponse.error("删除失败");
    }
}