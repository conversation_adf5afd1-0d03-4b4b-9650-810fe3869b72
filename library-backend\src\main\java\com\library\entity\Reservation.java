package com.library.entity;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class Reservation {
    private Long id;
    private Long userId;
    private Long bookId;
    private LocalDateTime reservationDate;
    private String status; // WAITING, NOTIFIED, CANCELLED, FULFILLED
    private LocalDateTime notifyDate;
    private LocalDateTime expireDate;
    
    // 关联对象
    private User user;
    private Book book;
}