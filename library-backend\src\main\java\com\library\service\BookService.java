package com.library.service;

import com.library.entity.Book;
import com.library.entity.Category;
import com.library.mapper.BookMapper;
import com.library.mapper.CategoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class BookService {
    
    @Autowired
    private BookMapper bookMapper;
    
    @Autowired
    private CategoryMapper categoryMapper;
    
    public Book findById(Long id) {
        return bookMapper.findById(id);
    }
    
    public List<Book> findAll() {
        return bookMapper.findAll();
    }
    
    @Transactional
    public boolean create(Book book, List<Long> categoryIds) {
        // 检查ISBN是否已存在
        if (bookMapper.findByIsbn(book.getIsbn()) != null) {
            return false;
        }
        
        // 插入图书
        if (bookMapper.insert(book) > 0) {
            // 插入图书分类关系
            if (categoryIds != null && !categoryIds.isEmpty()) {
                for (Long categoryId : categoryIds) {
                    bookMapper.insertBookCategory(book.getId(), categoryId);
                }
            }
            return true;
        }
        return false;
    }
    
    @Transactional
    public boolean update(Book book, List<Long> categoryIds) {
        // 更新图书信息
        if (bookMapper.update(book) > 0) {
            // 删除原有分类关系
            bookMapper.deleteBookCategories(book.getId());
            
            // 插入新的分类关系
            if (categoryIds != null && !categoryIds.isEmpty()) {
                for (Long categoryId : categoryIds) {
                    bookMapper.insertBookCategory(book.getId(), categoryId);
                }
            }
            return true;
        }
        return false;
    }
    
    @Transactional
    public boolean deleteById(Long id) {
        // 先删除分类关系
        bookMapper.deleteBookCategories(id);
        // 再删除图书
        return bookMapper.deleteById(id) > 0;
    }
}