package com.library.service;

import com.library.mapper.StatisticsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class StatisticsService {
    
    @Autowired
    private StatisticsMapper statisticsMapper;
    
    public Map<String, Object> getOverviewStats() {
        return statisticsMapper.getOverviewStats();
    }
    
    public List<Map<String, Object>> getBorrowStatsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return statisticsMapper.getBorrowStatsByDate(startDate, endDate);
    }
    
    public List<Map<String, Object>> getTopBorrowedBooks(int limit) {
        return statisticsMapper.getTopBorrowedBooks(limit);
    }
    
    public List<Map<String, Object>> getTopActiveUsers(LocalDateTime startDate, LocalDateTime endDate, int limit) {
        return statisticsMapper.getTopActiveUsers(startDate, endDate, limit);
    }
    
    public List<Map<String, Object>> getCategoryBorrowStats(LocalDateTime startDate, LocalDateTime endDate) {
        return statisticsMapper.getCategoryBorrowStats(startDate, endDate);
    }
    
    public List<Map<String, Object>> getMonthlyBorrowTrend() {
        return statisticsMapper.getMonthlyBorrowTrend();
    }
    
    public List<Map<String, Object>> getLowStockBooks(int threshold) {
        return statisticsMapper.getLowStockBooks(threshold);
    }
    
    public Map<String, Object> getComprehensiveStats() {
        Map<String, Object> result = new HashMap<>();
        
        // 概览统计
        result.put("overview", getOverviewStats());
        
        // 最近30天的借阅统计
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(30);
        result.put("recentBorrows", getBorrowStatsByDateRange(startDate, endDate));
        
        // 热门图书TOP10
        result.put("topBooks", getTopBorrowedBooks(10));
        
        // 活跃读者TOP10
        result.put("topUsers", getTopActiveUsers(startDate, endDate, 10));
        
        // 分类借阅统计
        result.put("categoryStats", getCategoryBorrowStats(startDate, endDate));
        
        // 月度趋势
        result.put("monthlyTrend", getMonthlyBorrowTrend());
        
        // 库存预警（库存<=5的图书）
        result.put("lowStockBooks", getLowStockBooks(5));
        
        return result;
    }
}