package com.library.service;

import com.library.entity.Notification;
import com.library.mapper.NotificationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NotificationService {
    
    @Autowired
    private NotificationMapper notificationMapper;
    
    public List<Notification> findByUserId(Long userId) {
        return notificationMapper.findByUserId(userId);
    }
    
    public List<Notification> findUnreadByUserId(Long userId) {
        return notificationMapper.findUnreadByUserId(userId);
    }
    
    public int countUnreadByUserId(Long userId) {
        return notificationMapper.countUnreadByUserId(userId);
    }
    
    public boolean markAsRead(Long id) {
        return notificationMapper.markAsRead(id) > 0;
    }
    
    public boolean markAllAsRead(Long userId) {
        return notificationMapper.markAllAsRead(userId) > 0;
    }
    
    public boolean create(Notification notification) {
        return notificationMapper.insert(notification) > 0;
    }
    
    public boolean delete(Long id) {
        return notificationMapper.deleteById(id) > 0;
    }
}