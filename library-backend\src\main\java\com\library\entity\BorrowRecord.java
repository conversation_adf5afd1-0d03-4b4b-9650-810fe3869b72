package com.library.entity;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class BorrowRecord {
    private Long id;
    private Long userId;
    private Long bookId;
    private LocalDateTime borrowDate;
    private LocalDateTime dueDate;
    private LocalDateTime returnDate;
    private String status; // BORROWED, RETURNED, OVERDUE
    private Integer renewCount;
    
    // 关联对象
    private User user;
    private Book book;
}