package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.BorrowRecord;
import com.library.service.BorrowService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/borrow")
@CrossOrigin
public class BorrowController {
    
    @Autowired
    private BorrowService borrowService;
    
    @GetMapping
    public ApiResponse<List<BorrowRecord>> getAllRecords(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if ("ADMIN".equals(role)) {
            return ApiResponse.success(borrowService.findAll());
        }
        return ApiResponse.error(403, "权限不足");
    }
    
    @GetMapping("/my")
    public ApiResponse<List<BorrowRecord>> getMyRecords(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        return ApiResponse.success(borrowService.findByUserId(userId));
    }
    
    @GetMapping("/user/{userId}")
    public ApiResponse<List<BorrowRecord>> getUserRecords(@PathVariable Long userId, HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(borrowService.findByUserId(userId));
    }
    
    @PostMapping("/borrow")
    public ApiResponse<String> borrowBook(@RequestBody Map<String, Long> params, HttpServletRequest request) {
        Long bookId = params.get("bookId");
        Long userId = (Long) request.getAttribute("userId");
        
        String error = borrowService.borrowBook(userId, bookId);
        if (error == null) {
            return ApiResponse.success("借阅成功", null);
        }
        return ApiResponse.error(error);
    }
    
    @PostMapping("/return/{recordId}")
    public ApiResponse<String> returnBook(@PathVariable Long recordId) {
        String error = borrowService.returnBook(recordId);
        if (error == null) {
            return ApiResponse.success("归还成功", null);
        }
        return ApiResponse.error(error);
    }
    
    @PostMapping("/renew/{recordId}")
    public ApiResponse<String> renewBook(@PathVariable Long recordId) {
        String error = borrowService.renewBook(recordId);
        if (error == null) {
            return ApiResponse.success("续借成功", null);
        }
        return ApiResponse.error(error);
    }
    
    @GetMapping("/overdue")
    public ApiResponse<List<BorrowRecord>> getOverdueRecords(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(borrowService.findByStatus("OVERDUE"));
    }
}