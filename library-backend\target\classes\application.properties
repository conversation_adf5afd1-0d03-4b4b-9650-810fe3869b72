# \u670D\u52A1\u5668\u914D\u7F6E
server.port=8080

# \u5E94\u7528\u914D\u7F6E
spring.application.name=library-backend

# \u6570\u636E\u6E90\u914D\u7F6E
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456

# Jackson \u914D\u7F6E
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# MyBatis \u914D\u7F6E
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.library.entity
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# JWT \u914D\u7F6E
jwt.secret=MyJwtSecretKeyForLibraryManagementSystem2024
jwt.expiration=86400000  

# \u65E5\u5FD7\u914D\u7F6E
logging.level.com.library.mapper=debug

# \u7981\u7528Spring Security\u9ED8\u8BA4\u914D\u7F6E
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration