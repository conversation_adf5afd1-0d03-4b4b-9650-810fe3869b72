<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2 class="login-title">图书管理系统</h2>
      <el-form ref="formRef" :model="loginForm" :rules="rules" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
          />
        </el-form-item>
        <el-form-item>
          <el-button
              type="primary"
              @click="handleLogin"
              :loading="loading"
              style="width: 100%"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
      <div class="login-tips">
        <p>测试账号：</p>
        <p>管理员 - admin / 123456</p>
        <p>普通用户 - lisa / 123456</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { authApi } from '../api/auth'

const router = useRouter()
const loading = ref(false)

// 使用更明确的名称区分表单数据和表单引用
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单引用变量
const formRef = ref(null)

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  // 1. 表单验证
  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.warning('请填写完整的登录信息')
    return
  }

  loading.value = true

  try {
    // 2. 发送登录请求
    const res = await authApi.login(loginForm)

    // 3. 处理响应
    if (res.code === 200 && res.data.token) {
      localStorage.setItem('token', res.data.token)
      localStorage.setItem('user', JSON.stringify(res.data.user))
      ElMessage.success('登录成功')

      // 4. 根据用户角色跳转
      const routePath = res.data.user.role === 'admin' ? '/admin' : '/user'
      router.push(routePath)
    } else {
      ElMessage.error(res.message || '登录失败，请检查账号密码')
    }
  } catch (error) {
    // 5. 增强错误处理
    let errorMessage = '登录失败'

    if (error.response) {
      // HTTP 错误响应 (4xx, 5xx)
      switch (error.response.status) {
        case 401:
          errorMessage = '用户名或密码错误'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = `请求错误: ${error.response.status}`
      }
    } else if (error.request) {
      // 请求已发送但无响应
      errorMessage = '网络错误，请检查网络连接'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}
</script>
<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  padding: 20px;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.login-tips {
  margin-top: 20px;
  font-size: 12px;
  color: #666;
  text-align: center;
}

.login-tips p {
  margin: 5px 0;
}
</style>