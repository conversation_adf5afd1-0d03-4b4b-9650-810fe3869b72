<template>
  <!-- 添加根元素并设置 id="app" -->
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
// 可以添加全局逻辑（如用户认证检查）
</script>

<style>
/* 全局样式 */
#app {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;

  /* 建议添加最小高度 */
  min-height: 100vh;

  /* 添加背景色（可选） */
  background-color: #f5f7fa;
}

body {
  margin: 0;
  padding: 0;

  /* 添加全局字体（可选） */
  font-family: inherit; /* 继承 #app 的字体 */
}

/* 添加全局过渡效果（可选） */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>