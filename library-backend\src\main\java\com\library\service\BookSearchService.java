package com.library.service;

import com.library.dto.BookSearchRequest;
import com.library.entity.Book;
import com.library.mapper.BookSearchMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BookSearchService {
    
    @Autowired
    private BookSearchMapper bookSearchMapper;
    
    public Map<String, Object> searchBooks(BookSearchRequest request) {
        // 确保分页参数有效
        if (request.getPage() == null || request.getPage() < 1) {
            request.setPage(1);
        }
        if (request.getPageSize() == null || request.getPageSize() < 1) {
            request.setPageSize(10);
        }
        
        List<Book> books = bookSearchMapper.searchBooks(request);
        int total = bookSearchMapper.countSearchBooks(request);
        
        Map<String, Object> result = new HashMap<>();
        result.put("books", books);
        result.put("total", total);
        result.put("page", request.getPage());
        result.put("pageSize", request.getPageSize());
        result.put("totalPages", (int) Math.ceil((double) total / request.getPageSize()));
        
        return result;
    }
}