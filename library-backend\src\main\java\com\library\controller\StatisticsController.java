package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.service.StatisticsService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
@CrossOrigin
public class StatisticsController {
    
    @Autowired
    private StatisticsService statisticsService;
    
    @GetMapping("/overview")
    public ApiResponse<Map<String, Object>> getOverview(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(statisticsService.getOverviewStats());
    }
    
    @GetMapping("/comprehensive")
    public ApiResponse<Map<String, Object>> getComprehensiveStats(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(statisticsService.getComprehensiveStats());
    }
    
    @GetMapping("/borrow-trend")
    public ApiResponse<List<Map<String, Object>>> getBorrowTrend(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(statisticsService.getBorrowStatsByDateRange(startDate, endDate));
    }
    
    @GetMapping("/top-books")
    public ApiResponse<List<Map<String, Object>>> getTopBooks(
            @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(statisticsService.getTopBorrowedBooks(limit));
    }
    
    @GetMapping("/top-users")
    public ApiResponse<List<Map<String, Object>>> getTopUsers(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(statisticsService.getTopActiveUsers(startDate, endDate, limit));
    }
    
    @GetMapping("/category-stats")
    public ApiResponse<List<Map<String, Object>>> getCategoryStats(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(statisticsService.getCategoryBorrowStats(startDate, endDate));
    }
    
    @GetMapping("/monthly-trend")
    public ApiResponse<List<Map<String, Object>>> getMonthlyTrend(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return ApiResponse.error(403, "权限不足");
        }
        return ApiResponse.success(statisticsService.getMonthlyBorrowTrend());
    }
}