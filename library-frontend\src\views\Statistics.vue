<template>
  <div class="statistics-container">
    <!-- 概览卡片 -->
    <el-row :gutter="20" class="overview-row">
      <el-col :span="4" v-for="item in overviewItems" :key="item.key">
        <el-card class="stat-card">
          <div class="stat-value">{{ stats.overview[item.key] || 0 }}</div>
          <div class="stat-label">{{ item.label }}</div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 月度趋势 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>月度借阅趋势</span>
          </template>
          <div ref="monthlyChart" style="height: 300px"></div>
        </el-card>
      </el-col>
      
      <!-- 分类统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>分类借阅统计</span>
          </template>
          <div ref="categoryChart" style="height: 300px"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 排行榜 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 热门图书 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>热门图书 TOP10</span>
          </template>
          <el-table :data="stats.topBooks" height="400">
            <el-table-column type="index" label="排名" width="60" />
            <el-table-column prop="title" label="书名" />
            <el-table-column prop="author" label="作者" width="120" />
            <el-table-column prop="borrowCount" label="借阅次数" width="100">
              <template #default="scope">
                <el-tag>{{ scope.row.borrowCount }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <!-- 活跃读者 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>活跃读者 TOP10</span>
          </template>
          <el-table :data="stats.topUsers" height="400">
            <el-table-column type="index" label="排名" width="60" />
            <el-table-column prop="nickname" label="姓名" />
            <el-table-column prop="borrowCount" label="借阅次数" width="100">
              <template #default="scope">
                <el-tag type="success">{{ scope.row.borrowCount }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 库存预警 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>库存预警</span>
      </template>
      <el-table :data="stats.lowStockBooks">
        <el-table-column prop="title" label="书名" />
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="stock" label="总库存" width="100">
          <template #default="scope">
            <el-tag type="warning">{{ scope.row.stock }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="available_stock" label="可借数量" width="100">
          <template #default="scope">
            <el-tag type="danger">{{ scope.row.available_stock }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { statisticsApi } from '../api/statistics'
// import * as echarts from 'echarts'  // 暂时注释，避免报错

const stats = ref({
  overview: {},
  monthlyTrend: [],
  categoryStats: [],
  topBooks: [],
  topUsers: [],
  lowStockBooks: []
})

const overviewItems = [
  { key: 'totalBooks', label: '图书总数' },
  { key: 'totalUsers', label: '用户总数' },
  { key: 'activeBorrows', label: '当前借阅' },
  { key: 'overdueCount', label: '逾期数量' },
  { key: 'todayBorrows', label: '今日借出' },
  { key: 'todayReturns', label: '今日归还' }
]

const monthlyChart = ref(null)
const categoryChart = ref(null)

const loadStatistics = async () => {
  try {
    const res = await statisticsApi.getComprehensive()
    stats.value = res.data
    
    await nextTick()
    drawCharts()
  } catch (error) {
    console.error(error)
  }
}

const drawCharts = () => {
  // 暂时禁用图表，避免echarts导入错误
  console.log('Charts disabled temporarily')
  /*
  // 月度趋势图
  if (monthlyChart.value && window.echarts) {
    const chart = window.echarts.init(monthlyChart.value)
    // ... chart code
  }
  */
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.statistics-container {
  padding: 20px;
}

.overview-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}
</style>