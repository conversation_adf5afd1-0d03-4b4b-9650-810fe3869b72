<template>
  <div class="notifications-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>消息中心</span>
          <el-button v-if="unreadCount > 0" @click="markAllAsRead">全部标记已读</el-button>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="全部消息" name="all">
          <div class="notification-list">
            <div 
              v-for="notification in allNotifications" 
              :key="notification.id"
              class="notification-item"
              :class="{ unread: !notification.isRead }"
              @click="handleRead(notification)"
            >
              <div class="notification-header">
                <el-tag :type="getTypeTag(notification.type)" size="small">
                  {{ getTypeText(notification.type) }}
                </el-tag>
                <span class="notification-time">{{ formatDateTime(notification.createTime) }}</span>
              </div>
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-content">{{ notification.content }}</div>
            </div>
          </div>
          <el-empty v-if="allNotifications.length === 0" description="暂无消息" />
        </el-tab-pane>
        
        <el-tab-pane :label="`未读消息(${unreadCount})`" name="unread">
          <div class="notification-list">
            <div 
              v-for="notification in unreadNotifications" 
              :key="notification.id"
              class="notification-item unread"
              @click="handleRead(notification)"
            >
              <div class="notification-header">
                <el-tag :type="getTypeTag(notification.type)" size="small">
                  {{ getTypeText(notification.type) }}
                </el-tag>
                <span class="notification-time">{{ formatDateTime(notification.createTime) }}</span>
              </div>
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-content">{{ notification.content }}</div>
            </div>
          </div>
          <el-empty v-if="unreadNotifications.length === 0" description="暂无未读消息" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { notificationApi } from '../api/notification'
import dayjs from 'dayjs'

const activeTab = ref('all')
const notifications = ref([])
const unreadCount = ref(0)
const loading = ref(false)

const allNotifications = computed(() => notifications.value)
const unreadNotifications = computed(() => 
  notifications.value.filter(n => !n.isRead)
)

const formatDateTime = (datetime) => {
  return datetime ? dayjs(datetime).format('YYYY-MM-DD HH:mm') : ''
}

const getTypeTag = (type) => {
  const tags = {
    'SYSTEM': 'info',
    'BORROW': 'primary',
    'RETURN': 'success',
    'OVERDUE': 'danger',
    'RESERVATION': 'warning'
  }
  return tags[type] || 'info'
}

const getTypeText = (type) => {
  const texts = {
    'SYSTEM': '系统',
    'BORROW': '借阅',
    'RETURN': '归还',
    'OVERDUE': '逾期',
    'RESERVATION': '预约'
  }
  return texts[type] || type
}

const loadNotifications = async () => {
  loading.value = true
  try {
    const res = await notificationApi.getMyNotifications()
    notifications.value = res.data.notifications || []
    unreadCount.value = res.data.unreadCount || 0
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleRead = async (notification) => {
  if (!notification.isRead) {
    try {
      await notificationApi.markAsRead(notification.id)
      notification.isRead = true
      unreadCount.value--
    } catch (error) {
      console.error(error)
    }
  }
}

const markAllAsRead = async () => {
  try {
    await notificationApi.markAllAsRead()
    notifications.value.forEach(n => n.isRead = true)
    unreadCount.value = 0
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  loadNotifications()
})
</script>

<style scoped>
.notifications-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-list {
  max-height: 600px;
  overflow-y: auto;
}

.notification-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.notification-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}
</style>