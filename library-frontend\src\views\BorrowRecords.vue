<template>
  <div class="borrow-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>借阅记录</span>
          <div>
            <el-button @click="handleExport" v-if="isAdmin">导出记录</el-button>
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="当前借阅" name="current">
          <el-table :data="currentRecords" v-loading="loading" style="width: 100%">
            <el-table-column prop="book.title" label="书名" />
            <el-table-column prop="book.author" label="作者" width="120" />
            <el-table-column prop="borrowDate" label="借阅时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.borrowDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="dueDate" label="应还时间" width="180">
              <template #default="scope">
                <el-text :type="isOverdue(scope.row.dueDate) ? 'danger' : ''">
                  {{ formatDateTime(scope.row.dueDate) }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column prop="renewCount" label="已续借" width="80">
              <template #default="scope">
                {{ scope.row.renewCount }}次
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" @click="handleReturn(scope.row)">归还</el-button>
                <el-button size="small" @click="handleRenew(scope.row)" 
                          :disabled="scope.row.renewCount >= 2">续借</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="历史记录" name="history">
          <el-table :data="historyRecords" v-loading="loading" style="width: 100%">
            <el-table-column prop="book.title" label="书名" />
            <el-table-column prop="book.author" label="作者" width="120" />
            <el-table-column prop="borrowDate" label="借阅时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.borrowDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="returnDate" label="归还时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.returnDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="逾期记录" name="overdue" v-if="isAdmin">
          <el-table :data="overdueRecords" v-loading="loading" style="width: 100%">
            <el-table-column prop="user.nickname" label="借阅人" width="120" />
            <el-table-column prop="book.title" label="书名" />
            <el-table-column prop="borrowDate" label="借阅时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.borrowDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="dueDate" label="应还时间" width="180">
              <template #default="scope">
                <el-text type="danger">
                  {{ formatDateTime(scope.row.dueDate) }}
                </el-text>
              </template>
            </el-table-column>
            <el-table-column label="逾期天数" width="100">
              <template #default="scope">
                <el-text type="danger">
                  {{ calculateOverdueDays(scope.row.dueDate) }}天
                </el-text>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { borrowApi } from '../api/borrow'
import dayjs from 'dayjs'

const loading = ref(false)
const activeTab = ref('current')
const records = ref([])
const overdueRecords = ref([])

const user = JSON.parse(localStorage.getItem('user') || '{}')
const isAdmin = computed(() => user.role === 'ADMIN')

const currentRecords = computed(() => 
  records.value.filter(r => r.status === 'BORROWED')
)

const historyRecords = computed(() => 
  records.value.filter(r => r.status === 'RETURNED')
)

const formatDateTime = (datetime) => {
  return datetime ? dayjs(datetime).format('YYYY-MM-DD HH:mm') : ''
}

const isOverdue = (dueDate) => {
  return dayjs().isAfter(dayjs(dueDate))
}

const calculateOverdueDays = (dueDate) => {
  return dayjs().diff(dayjs(dueDate), 'day')
}

const getStatusType = (status) => {
  const types = {
    'BORROWED': 'primary',
    'RETURNED': 'success',
    'OVERDUE': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'BORROWED': '借阅中',
    'RETURNED': '已归还',
    'OVERDUE': '已逾期'
  }
  return texts[status] || status
}

const loadRecords = async () => {
  loading.value = true
  try {
    const res = await borrowApi.getMyRecords()
    records.value = res.data || []
    
    if (isAdmin.value) {
      const overdueRes = await borrowApi.getOverdueRecords()
      overdueRecords.value = overdueRes.data || []
    }
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleReturn = async (record) => {
  try {
    await ElMessageBox.confirm('确定要归还这本书吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await borrowApi.returnBook(record.id)
    ElMessage.success('归还成功')
    loadRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const handleRenew = async (record) => {
  try {
    await ElMessageBox.confirm('确定要续借这本书吗？每次续借延长15天', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    await borrowApi.renewBook(record.id)
    ElMessage.success('续借成功')
    loadRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const handleExport = async () => {
  try {
    const response = await fetch('http://localhost:8080/api/export/borrow-records', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `借阅记录_${dayjs().format('YYYYMMDD')}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  loadRecords()
})
</script>

<style scoped>
.borrow-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>