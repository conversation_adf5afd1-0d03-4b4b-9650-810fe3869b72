package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.dto.BookSearchRequest;
import com.library.service.BookSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/books/search")
@CrossOrigin
public class BookSearchController {
    
    @Autowired
    private BookSearchService bookSearchService;
    
    @PostMapping
    public ApiResponse<Map<String, Object>> searchBooks(@RequestBody BookSearchRequest request) {
        Map<String, Object> result = bookSearchService.searchBooks(request);
        return ApiResponse.success(result);
    }
}