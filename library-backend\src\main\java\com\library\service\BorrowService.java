package com.library.service;

import com.library.entity.BorrowRecord;
import com.library.entity.Notification;
import com.library.entity.SystemConfig;
import com.library.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class BorrowService {
    
    @Autowired
    private BorrowRecordMapper borrowRecordMapper;
    
    @Autowired
    private BookMapper bookMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private SystemConfigMapper systemConfigMapper;
    
    @Autowired
    private NotificationMapper notificationMapper;
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    public List<BorrowRecord> findAll() {
        return borrowRecordMapper.findAll();
    }
    
    public List<BorrowRecord> findByUserId(Long userId) {
        return borrowRecordMapper.findByUserId(userId);
    }
    
    public List<BorrowRecord> findByStatus(String status) {
        return borrowRecordMapper.findByStatus(status);
    }
    
    public BorrowRecord findById(Long id) {
        return borrowRecordMapper.findById(id);
    }
    
    @Transactional
    public String borrowBook(Long userId, Long bookId) {
        // 检查用户是否激活
        var user = userMapper.findById(userId);
        if (user == null || !user.getIsActive()) {
            return "用户不存在或未激活";
        }
        
        // 检查用户借阅数量
        int currentBorrowCount = borrowRecordMapper.countActiveByUserId(userId);
        if (currentBorrowCount >= user.getMaxBorrowCount()) {
            return "已达到最大借阅数量限制";
        }
        
        // 检查图书是否可借
        var book = bookMapper.findById(bookId);
        if (book == null || book.getAvailableStock() <= 0) {
            return "图书不可借阅";
        }
        
        // 获取借阅天数配置
        var borrowDaysConfig = systemConfigMapper.findByKey("borrow_days");
        int borrowDays = Integer.parseInt(borrowDaysConfig.getConfigValue());
        
        // 创建借阅记录
        BorrowRecord record = new BorrowRecord();
        record.setUserId(userId);
        record.setBookId(bookId);
        record.setDueDate(LocalDateTime.now().plusDays(borrowDays));
        record.setStatus("BORROWED");
        record.setRenewCount(0);
        
        // 更新图书库存
        if (bookMapper.borrowBook(bookId) > 0 && borrowRecordMapper.insert(record) > 0) {
            // 发送借阅成功通知
            Notification notification = new Notification();
            notification.setUserId(userId);
            notification.setTitle("借阅成功");
            notification.setContent(String.format("您已成功借阅《%s》，请在%d天内归还", 
                    book.getTitle(), borrowDays));
            notification.setType("BORROW");
            notificationMapper.insert(notification);
            
            return null; // 成功
        }
        
        return "借阅失败";
    }
    
    @Transactional
    public String returnBook(Long recordId) {
        var record = borrowRecordMapper.findById(recordId);
        if (record == null || !"BORROWED".equals(record.getStatus())) {
            return "借阅记录不存在或已归还";
        }
        
        record.setReturnDate(LocalDateTime.now());
        record.setStatus("RETURNED");
        
        if (borrowRecordMapper.updateReturn(record) > 0 && bookMapper.returnBook(record.getBookId()) > 0) {
            // 发送归还成功通知
            Notification notification = new Notification();
            notification.setUserId(record.getUserId());
            notification.setTitle("归还成功");
            notification.setContent(String.format("您已成功归还《%s》", record.getBook().getTitle()));
            notification.setType("RETURN");
            notificationMapper.insert(notification);
            
            // 检查是否有预约，通知第一个预约用户
            var reservations = reservationMapper.findWaitingByBookId(record.getBookId());
            if (!reservations.isEmpty()) {
                var firstReservation = reservations.get(0);
                firstReservation.setStatus("NOTIFIED");
                firstReservation.setNotifyDate(LocalDateTime.now());
                
                var expireDaysConfig = systemConfigMapper.findByKey("reservation_expire_days");
                int expireDays = Integer.parseInt(expireDaysConfig.getConfigValue());
                firstReservation.setExpireDate(LocalDateTime.now().plusDays(expireDays));
                
                reservationMapper.update(firstReservation);
                
                // 发送预约通知
                Notification reserveNotice = new Notification();
                reserveNotice.setUserId(firstReservation.getUserId());
                reserveNotice.setTitle("预约图书已到");
                reserveNotice.setContent(String.format("您预约的《%s》已到，请在%d天内前来借阅", 
                        record.getBook().getTitle(), expireDays));
                reserveNotice.setType("RESERVATION");
                notificationMapper.insert(reserveNotice);
            }
            
            return null; // 成功
        }
        
        return "归还失败";
    }
    
    @Transactional
    public String renewBook(Long recordId) {
        var record = borrowRecordMapper.findById(recordId);
        if (record == null || !"BORROWED".equals(record.getStatus())) {
            return "借阅记录不存在或已归还";
        }
        
        // 检查是否可以续借
        var allowRenewConfig = systemConfigMapper.findByKey("allow_renew");
        if (!"true".equals(allowRenewConfig.getConfigValue())) {
            return "系统暂不支持续借";
        }
        
        // 检查续借次数
        var maxRenewConfig = systemConfigMapper.findByKey("max_renew_count");
        int maxRenewCount = Integer.parseInt(maxRenewConfig.getConfigValue());
        if (record.getRenewCount() >= maxRenewCount) {
            return "已达到最大续借次数";
        }
        
        // 检查是否有人预约
        var reservations = reservationMapper.findWaitingByBookId(record.getBookId());
        if (!reservations.isEmpty()) {
            return "该图书已被预约，无法续借";
        }
        
        // 续借
        var renewDaysConfig = systemConfigMapper.findByKey("renew_days");
        int renewDays = Integer.parseInt(renewDaysConfig.getConfigValue());
        
        record.setDueDate(record.getDueDate().plusDays(renewDays));
        record.setRenewCount(record.getRenewCount() + 1);
        
        if (borrowRecordMapper.updateRenew(record) > 0) {
            // 发送续借成功通知
            Notification notification = new Notification();
            notification.setUserId(record.getUserId());
            notification.setTitle("续借成功");
            notification.setContent(String.format("《%s》续借成功，已延长%d天", 
                    record.getBook().getTitle(), renewDays));
            notification.setType("BORROW");
            notificationMapper.insert(notification);
            
            return null; // 成功
        }
        
        return "续借失败";
    }
    
    @Transactional
    public void checkOverdueRecords() {
        var overdueRecords = borrowRecordMapper.findOverdueRecords();
        for (var record : overdueRecords) {
            // 更新状态为逾期
            borrowRecordMapper.updateStatus(record.getId(), "OVERDUE");
            
            // 发送逾期通知
            Notification notification = new Notification();
            notification.setUserId(record.getUserId());
            notification.setTitle("图书逾期提醒");
            notification.setContent(String.format("您借阅的《%s》已逾期，请尽快归还", 
                    record.getBook().getTitle()));
            notification.setType("OVERDUE");
            notificationMapper.insert(notification);
        }
    }
}