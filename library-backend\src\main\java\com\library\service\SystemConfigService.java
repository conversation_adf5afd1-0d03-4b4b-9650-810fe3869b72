package com.library.service;

import com.library.entity.SystemConfig;
import com.library.mapper.SystemConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SystemConfigService {
    
    @Autowired
    private SystemConfigMapper systemConfigMapper;
    
    public List<SystemConfig> findAll() {
        return systemConfigMapper.findAll();
    }
    
    public SystemConfig findByKey(String key) {
        return systemConfigMapper.findByKey(key);
    }
    
    public boolean update(SystemConfig config) {
        return systemConfigMapper.update(config) > 0;
    }
    
    public String getValue(String key) {
        var config = systemConfigMapper.findByKey(key);
        return config != null ? config.getConfigValue() : null;
    }
    
    public boolean setValue(String key, String value) {
        var config = systemConfigMapper.findByKey(key);
        if (config != null) {
            config.setConfigValue(value);
            return systemConfigMapper.update(config) > 0;
        }
        return false;
    }
}